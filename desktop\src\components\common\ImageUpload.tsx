// Image Upload Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n kullanımı
// ✅ Kural 19: Tema sistemi kullanımı

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Button,
  TextField,
  Typography,
  Avatar,
  IconButton,
  Alert,
  CircularProgress,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  Link as LinkIcon,
  ContentPaste as PasteIcon,
  Delete as DeleteIcon,
  PhotoCamera as CameraIcon
} from '@mui/icons-material'
import {
  validateImageFile,
  validateImageUrl,

  resizeImage,
  getImageFromClipboard,
  formatFileSize,
  createImagePreview,
  revokeImagePreview,
  IMAGE_CONFIG
} from '../../utils/imageUpload'
import { AppError } from '../../services/productService'

// ✅ Kural 14: Component props interface
interface ImageUploadProps {
  value?: string
  onChange: (value: string) => void
  onError?: (error: string) => void
  disabled?: boolean
  size?: 'small' | 'medium' | 'large'
  label?: string
  helperText?: string
}

// ✅ Kural 14: Standard component structure
export const ImageUpload: React.FC<ImageUploadProps> = ({
  value,
  onChange,
  onError,
  disabled = false,
  size = 'medium',
  label,
  helperText
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  // const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  
  // ✅ Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // ✅ Local state hooks
  const [urlInput, setUrlInput] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  
  // ========== 2. COMPUTED VALUES ==========
  const avatarSize = {
    small: 80,
    medium: 120,
    large: 160
  }[size]
  
  const hasImage = !!(value || previewUrl)
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle error
   */
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage)
    onError?.(errorMessage)
  }, [onError])
  
  /**
   * ✅ Clear error
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])
  
  /**
   * ✅ Handle file selection
   */
  const handleFileSelect = useCallback(async (file: File) => {
    clearError()
    setLoading(true)
    
    try {
      // Dosya validasyonu
      validateImageFile(file)
      
      // Resmi optimize et ve base64'e çevir
      const optimizedBase64 = await resizeImage(file)
      
      // Preview URL oluştur
      const preview = createImagePreview(file)
      setPreviewUrl(preview)
      
      // Parent component'e gönder
      onChange(optimizedBase64)
      
    } catch (error) {
      if (error instanceof AppError) {
        handleError(error.message)
      } else {
        handleError('Resim yüklenirken hata oluştu')
      }
    } finally {
      setLoading(false)
    }
  }, [onChange, handleError, clearError])
  
  /**
   * ✅ Handle file input change
   */
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
    // Input'u temizle
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [handleFileSelect])
  
  /**
   * ✅ Handle URL input
   */
  const handleUrlSubmit = useCallback(async () => {
    if (!urlInput.trim()) return
    
    clearError()
    setLoading(true)
    
    try {
      // URL validasyonu
      validateImageUrl(urlInput.trim())
      
      // URL'yi parent component'e gönder
      onChange(urlInput.trim())
      setUrlInput('')
      
    } catch (error) {
      if (error instanceof AppError) {
        handleError(error.message)
      } else {
        handleError('Geçersiz resim URL\'si')
      }
    } finally {
      setLoading(false)
    }
  }, [urlInput, onChange, handleError, clearError])
  
  /**
   * ✅ Handle paste from clipboard
   */
  const handlePasteFromClipboard = useCallback(async () => {
    clearError()
    setLoading(true)
    
    try {
      const clipboardFile = await getImageFromClipboard()
      
      if (clipboardFile) {
        await handleFileSelect(clipboardFile)
      } else {
        handleError('Panoda resim bulunamadı')
      }
    } catch (error) {
      handleError('Panodan resim alınamadı')
    } finally {
      setLoading(false)
    }
  }, [handleFileSelect, handleError, clearError])
  
  /**
   * ✅ Handle drag and drop
   */
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(true)
  }, [])
  
  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
  }, [])
  
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    setDragOver(false)
    
    const file = event.dataTransfer.files[0]
    if (file && file.type.startsWith('image/')) {
      handleFileSelect(file)
    } else {
      handleError('Lütfen geçerli bir resim dosyası sürükleyin')
    }
  }, [handleFileSelect, handleError])
  
  /**
   * ✅ Handle remove image
   */
  const handleRemoveImage = useCallback(() => {
    onChange('')
    setUrlInput('')
    if (previewUrl) {
      revokeImagePreview(previewUrl)
      setPreviewUrl(null)
    }
    clearError()
  }, [onChange, previewUrl, clearError])
  
  /**
   * ✅ Handle file button click
   */
  const handleFileButtonClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])
  
  // ========== 4. EFFECTS ==========
  
  /**
   * ✅ Cleanup preview URL on unmount
   */
  useEffect(() => {
    return () => {
      if (previewUrl) {
        revokeImagePreview(previewUrl)
      }
    }
  }, [previewUrl])
  
  // ========== 5. RENDER HELPERS ==========
  
  /**
   * ✅ Render image preview
   */
  const renderImagePreview = () => (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <Avatar
        src={previewUrl || value}
        sx={{
          width: avatarSize,
          height: avatarSize,
          border: `2px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.grey[100]
        }}
      >
        <CameraIcon sx={{ fontSize: avatarSize * 0.4 }} />
      </Avatar>
      
      {hasImage && !disabled && (
        <IconButton
          size="small"
          onClick={handleRemoveImage}
          sx={{
            position: 'absolute',
            top: -8,
            right: -8,
            backgroundColor: theme.palette.error.main,
            color: 'white',
            '&:hover': {
              backgroundColor: theme.palette.error.dark
            }
          }}
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      )}
    </Box>
  )
  
  /**
   * ✅ Render upload area
   */
  const renderUploadArea = () => (
    <Box
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      sx={{
        border: `2px dashed ${dragOver ? theme.palette.primary.main : theme.palette.divider}`,
        borderRadius: 2,
        p: 3,
        textAlign: 'center',
        backgroundColor: dragOver 
          ? alpha(theme.palette.primary.main, 0.05)
          : alpha(theme.palette.grey[50], 0.5),
        transition: 'all 0.2s ease',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1
      }}
      onClick={!disabled ? handleFileButtonClick : undefined}
    >
      <UploadIcon 
        sx={{ 
          fontSize: 48, 
          color: dragOver ? 'primary.main' : 'text.secondary',
          mb: 1
        }} 
      />
      
      <Typography variant="body1" gutterBottom>
        {dragOver 
          ? 'Resmi buraya bırakın'
          : 'Resim yüklemek için tıklayın veya sürükleyin'
        }
      </Typography>
      
      <Typography variant="caption" color="text.secondary">
        Maksimum {formatFileSize(IMAGE_CONFIG.MAX_FILE_SIZE)} • {IMAGE_CONFIG.ALLOWED_EXTENSIONS.join(', ')}
      </Typography>
    </Box>
  )
  
  // ========== 6. MAIN RENDER ==========
  return (
    <Box>
      {/* Label */}
      {label && (
        <Typography variant="subtitle2" gutterBottom>
          {label}
        </Typography>
      )}
      
      {/* Image Preview */}
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'center' }}>
        {renderImagePreview()}
      </Box>
      
      {/* Upload Methods */}
      {!disabled && (
        <Box sx={{ mb: 2 }}>
          {/* File Upload */}
          {renderUploadArea()}
          
          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 1, mt: 2, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              onClick={handleFileButtonClick}
              disabled={loading}
              size="small"
            >
              Dosya Seç
            </Button>
            
            <Tooltip title="Panodaki resmi yapıştır">
              <Button
                variant="outlined"
                startIcon={<PasteIcon />}
                onClick={handlePasteFromClipboard}
                disabled={loading}
                size="small"
              >
                Yapıştır
              </Button>
            </Tooltip>
          </Box>
          
          {/* URL Input */}
          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            <TextField
              fullWidth
              size="small"
              placeholder="Resim URL'si girin"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              disabled={loading}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  handleUrlSubmit()
                }
              }}
            />
            <Button
              variant="outlined"
              startIcon={<LinkIcon />}
              onClick={handleUrlSubmit}
              disabled={loading || !urlInput.trim()}
              size="small"
            >
              URL Ekle
            </Button>
          </Box>
        </Box>
      )}
      
      {/* Loading */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress size={24} />
        </Box>
      )}
      
      {/* Error */}
      {error && (
        <Alert severity="error" sx={{ mt: 1 }}>
          {error}
        </Alert>
      )}
      
      {/* Helper Text */}
      {helperText && !error && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          {helperText}
        </Typography>
      )}
      
      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={IMAGE_CONFIG.ALLOWED_FORMATS.join(',')}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
        disabled={disabled}
      />
    </Box>
  )
}
