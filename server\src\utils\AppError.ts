// AppError Class - DEVELOPMENT_RULES.md Kural 11'e uygun
// ✅ Custom Error Class pattern

export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public code: string,
    public details?: unknown
  ) {
    super(message)
    this.name = 'AppError'
    
    // Error.captureStackTrace varsa kullan (Node.js)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError)
    }
  }
}

// ✅ Kural 11: Error Response Format
export interface ErrorResponse {
  success: false
  error: string
  code?: string
  details?: unknown
}

// ✅ Predefined error codes
export const ErrorCodes = {
  // Product errors
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  PRODUCT_CODE_EXISTS: 'PRODUCT_CODE_EXISTS',
  PRODUCT_BARCODE_EXISTS: 'PRODUCT_BARCODE_EXISTS',
  PRODUCT_CATEGORY_NOT_FOUND: 'PRODUCT_CATEGORY_NOT_FOUND',
  PRODUCT_TAX_NOT_FOUND: 'PRODUCT_TAX_NOT_FOUND',
  PRODUCT_CREATION_FAILED: 'PRODUCT_CREATION_FAILED',
  PRODUCT_UPDATE_FAILED: 'PRODUCT_UPDATE_FAILED',
  PRODUCT_DELETE_FAILED: 'PRODUCT_DELETE_FAILED',
  
  // Category errors
  CATEGORY_NOT_FOUND: 'CATEGORY_NOT_FOUND',
  CATEGORY_HAS_PRODUCTS: 'CATEGORY_HAS_PRODUCTS',
  CATEGORY_NAME_EXISTS: 'CATEGORY_NAME_EXISTS',
  CATEGORY_CREATION_FAILED: 'CATEGORY_CREATION_FAILED',
  CATEGORY_UPDATE_FAILED: 'CATEGORY_UPDATE_FAILED',
  CATEGORY_DELETE_FAILED: 'CATEGORY_DELETE_FAILED',

  // Tax errors
  TAX_NOT_FOUND: 'TAX_NOT_FOUND',

  // General errors
  NOT_FOUND: 'NOT_FOUND',
  INVALID_INPUT: 'INVALID_INPUT',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  INVALID_OPERATION: 'INVALID_OPERATION',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR'
} as const

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes]
