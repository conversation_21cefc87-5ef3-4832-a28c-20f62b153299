// Category Form Modal - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n kullanımı
// ✅ Kural 19: Tema sistemi kullanımı

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Box,
  Typography,
  CircularProgress,
  InputAdornment,
  Divider,
  useTheme,
  useMediaQuery,
  IconButton
} from '@mui/material'
import {
  Save as SaveIcon,
  Close as CloseIcon,
  Palette as PaletteIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material'
import { categoryService, CreateCategoryRequest, UpdateCategoryRequest } from '../../services/categoryService'
import { Category } from '@shared/types/ProductTypes'
import { createCategorySchema, updateCategorySchema, formatValidationErrors } from '../../validators/categoryValidators'
import { z } from 'zod'

// ✅ Kural 14: Component props interface
interface CategoryFormModalProps {
  open: boolean
  category?: Category | null
  parentCategories?: Category[]
  onClose: () => void
  onSuccess: (category: Category, message: string) => void
  onError: (message: string) => void
}

// ✅ Form data interface
interface FormData {
  name: string
  description: string
  color: string
  icon: string
  parentId: string
  showInKitchen: boolean
  showInMenu: boolean
  preparationTime: string
  displayOrder: string
}

// ✅ Validation errors interface
interface ValidationErrors {
  [key: string]: string
}

// ✅ Default form data
const defaultFormData: FormData = {
  name: '',
  description: '',
  color: '#2196F3',
  icon: '',
  parentId: '',
  showInKitchen: true,
  showInMenu: true,
  preparationTime: '',
  displayOrder: '0'
}

// ✅ Predefined colors
const predefinedColors = [
  '#2196F3', '#4CAF50', '#FF9800', '#F44336', '#9C27B0',
  '#00BCD4', '#8BC34A', '#FFC107', '#E91E63', '#3F51B5',
  '#009688', '#CDDC39', '#FF5722', '#795548', '#607D8B'
]

// ✅ Predefined icons
const predefinedIcons = [
  'restaurant', 'local_dining', 'fastfood', 'local_pizza', 'local_cafe',
  'cake', 'icecream', 'local_bar', 'wine_bar', 'breakfast_dining',
  'lunch_dining', 'dinner_dining', 'ramen_dining', 'rice_bowl'
]

// ✅ Kural 14: Standard component structure
export const CategoryFormModal: React.FC<CategoryFormModalProps> = ({
  open,
  category,
  parentCategories = [],
  onClose,
  onSuccess,
  onError
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Local state hooks
  const [formData, setFormData] = useState<FormData>(defaultFormData)
  const [errors, setErrors] = useState<ValidationErrors>({})
  const [submitting, setSubmitting] = useState(false)
  
  // ========== 2. COMPUTED VALUES ==========
  
  /**
   * ✅ Kural 17: useMemo for computed values
   */
  const isEditing = useMemo(() => !!category, [category])
  
  const dialogTitle = useMemo(() => {
    return isEditing ? t('categories.form.edit') : t('categories.form.add')
  }, [isEditing, t])
  
  const submitButtonText = useMemo(() => {
    if (submitting) {
      return t('categories.form.saving')
    }
    return isEditing ? t('categories.form.update') : t('categories.form.create')
  }, [submitting, isEditing, t])
  
  // ========== 3. VALIDATION ==========
  
  /**
   * ✅ Validate form data
   */
  const validateForm = useCallback((): boolean => {
    try {
      const dataToValidate = {
        name: formData.name,
        description: formData.description || undefined,
        color: formData.color || undefined,
        icon: formData.icon || undefined,
        parentId: formData.parentId || undefined,
        showInKitchen: formData.showInKitchen,
        showInMenu: formData.showInMenu,
        preparationTime: formData.preparationTime ? parseInt(formData.preparationTime) : undefined,
        displayOrder: parseInt(formData.displayOrder)
      }
      
      if (isEditing) {
        updateCategorySchema.parse(dataToValidate)
      } else {
        createCategorySchema.parse(dataToValidate)
      }
      
      setErrors({})
      return true
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(formatValidationErrors(error))
      }
      return false
    }
  }, [formData, isEditing])
  
  // ========== 4. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle input change
   */
  const handleInputChange = useCallback((field: keyof FormData) => {
    return (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value
      setFormData(prev => ({ ...prev, [field]: value }))
      
      // Clear error for this field
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }))
      }
    }
  }, [errors])
  
  /**
   * ✅ Handle select change
   */
  const handleSelectChange = useCallback((field: keyof FormData) => {
    return (event: any) => {
      const value = event.target.value
      setFormData(prev => ({ ...prev, [field]: value }))
      
      // Clear error for this field
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }))
      }
    }
  }, [errors])
  
  /**
   * ✅ Handle switch change
   */
  const handleSwitchChange = useCallback((field: keyof FormData) => {
    return (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.checked
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }, [])
  
  /**
   * ✅ Handle color selection
   */
  const handleColorSelect = useCallback((color: string) => {
    setFormData(prev => ({ ...prev, color }))
    if (errors.color) {
      setErrors(prev => ({ ...prev, color: '' }))
    }
  }, [errors.color])
  
  /**
   * ✅ Handle icon selection
   */
  const handleIconSelect = useCallback((icon: string) => {
    setFormData(prev => ({ ...prev, icon }))
    if (errors.icon) {
      setErrors(prev => ({ ...prev, icon: '' }))
    }
  }, [errors.icon])
  
  /**
   * ✅ Handle form submit
   */
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault()
    
    if (!validateForm()) {
      onError(t('categories.messages.validationError') || 'Form verilerinde hata var')
      return
    }
    
    setSubmitting(true)
    
    try {
      const apiData: CreateCategoryRequest | UpdateCategoryRequest = {
        name: formData.name,
        description: formData.description || undefined,
        color: formData.color || undefined,
        icon: formData.icon || undefined,
        parentId: formData.parentId || undefined,
        showInKitchen: formData.showInKitchen,
        showInMenu: formData.showInMenu,
        preparationTime: formData.preparationTime ? parseInt(formData.preparationTime) : undefined,
        displayOrder: parseInt(formData.displayOrder)
      }
      
      let response
      if (isEditing && category) {
        response = await categoryService.updateCategory(category.id, apiData as UpdateCategoryRequest)

        // Convert service category to shared category type
        const updatedCategory: Category = {
          id: response.data.id,
          companyId: response.data.companyId,
          parentId: response.data.parentId,
          name: response.data.name,
          description: response.data.description,
          image: response.data.image,
          color: response.data.color,
          icon: response.data.icon,
          showInKitchen: response.data.showInKitchen,
          preparationTime: response.data.preparationTime,
          displayOrder: response.data.displayOrder,
          active: response.data.active,
          showInMenu: response.data.showInMenu,
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
          deletedAt: undefined
        }

        onSuccess(updatedCategory, t('categories.messages.updateSuccess') || 'Kategori başarıyla güncellendi')
      } else {
        response = await categoryService.createCategory(apiData as CreateCategoryRequest)

        // Convert service category to shared category type
        const newCategory: Category = {
          id: response.data.id,
          companyId: response.data.companyId,
          parentId: response.data.parentId,
          name: response.data.name,
          description: response.data.description,
          image: response.data.image,
          color: response.data.color,
          icon: response.data.icon,
          showInKitchen: response.data.showInKitchen,
          preparationTime: response.data.preparationTime,
          displayOrder: response.data.displayOrder,
          active: response.data.active,
          showInMenu: response.data.showInMenu,
          createdAt: response.data.createdAt,
          updatedAt: response.data.updatedAt,
          deletedAt: undefined
        }

        onSuccess(newCategory, t('categories.messages.createSuccess') || 'Kategori başarıyla oluşturuldu')
      }
      
      handleClose()
    } catch (error: any) {
      const errorMessage = error.message || (isEditing 
        ? t('categories.messages.updateError') 
        : t('categories.messages.createError'))
      onError(errorMessage)
    } finally {
      setSubmitting(false)
    }
  }, [formData, validateForm, isEditing, category, onSuccess, onError, t])
  
  /**
   * ✅ Handle close
   */
  const handleClose = useCallback(() => {
    if (!submitting) {
      setFormData(defaultFormData)
      setErrors({})
      onClose()
    }
  }, [submitting, onClose])
  
  // ========== 5. EFFECTS ==========
  
  /**
   * ✅ Initialize form data when category changes
   */
  useEffect(() => {
    if (category && open) {
      setFormData({
        name: category.name || '',
        description: category.description || '',
        color: category.color || '#2196F3',
        icon: category.icon || '',
        parentId: category.parentId || '',
        showInKitchen: category.showInKitchen,
        showInMenu: category.showInMenu,
        preparationTime: category.preparationTime?.toString() || '',
        displayOrder: category.displayOrder?.toString() || '0'
      })
    } else if (open && !category) {
      setFormData(defaultFormData)
    }
    setErrors({})
  }, [category, open])
  
  // ========== 6. RENDER HELPERS ==========

  /**
   * ✅ Render basic information section
   */
  const renderBasicInfo = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('categories.form.basicInfo')}
      </Typography>

      <Grid container spacing={2}>
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('categories.name')}
            value={formData.name}
            onChange={handleInputChange('name')}
            error={!!errors.name}
            helperText={errors.name}
            placeholder={t('categories.form.namePlaceholder')}
            required
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <FormControl fullWidth>
            <InputLabel>{t('categories.parent')}</InputLabel>
            <Select
              value={formData.parentId}
              onChange={handleSelectChange('parentId')}
              label={t('categories.parent')}
            >
              <MenuItem value="">
                <em>{t('common.none')}</em>
              </MenuItem>
              {parentCategories.map((cat) => (
                <MenuItem key={cat.id} value={cat.id}>
                  {cat.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid size={{ xs: 12 }}>
          <TextField
            fullWidth
            multiline
            rows={3}
            label={t('categories.description')}
            value={formData.description}
            onChange={handleInputChange('description')}
            error={!!errors.description}
            helperText={errors.description}
            placeholder={t('categories.form.descriptionPlaceholder')}
          />
        </Grid>
      </Grid>
    </Box>
  )

  /**
   * ✅ Render appearance section
   */
  const renderAppearance = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('categories.form.appearance')}
      </Typography>

      <Grid container spacing={2}>
        {/* Color Picker */}
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('categories.color')}
            value={formData.color}
            onChange={handleInputChange('color')}
            error={!!errors.color}
            helperText={errors.color}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      backgroundColor: formData.color,
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider'
                    }}
                  />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <PaletteIcon />
                </InputAdornment>
              )
            }}
          />

          {/* Predefined Colors */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
            {predefinedColors.map((color) => (
              <IconButton
                key={color}
                size="small"
                onClick={() => handleColorSelect(color)}
                sx={{
                  width: 32,
                  height: 32,
                  backgroundColor: color,
                  border: formData.color === color ? '2px solid' : '1px solid',
                  borderColor: formData.color === color ? 'primary.main' : 'divider',
                  '&:hover': {
                    backgroundColor: color,
                    opacity: 0.8
                  }
                }}
              />
            ))}
          </Box>
        </Grid>

        {/* Icon Picker */}
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            label={t('categories.icon')}
            value={formData.icon}
            onChange={handleInputChange('icon')}
            error={!!errors.icon}
            helperText={errors.icon}
            placeholder={t('categories.form.iconPlaceholder')}
          />

          {/* Predefined Icons */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
            {predefinedIcons.map((icon) => (
              <IconButton
                key={icon}
                size="small"
                onClick={() => handleIconSelect(icon)}
                sx={{
                  border: formData.icon === icon ? '2px solid' : '1px solid',
                  borderColor: formData.icon === icon ? 'primary.main' : 'divider'
                }}
              >
                <Box component="span" className="material-icons" sx={{ fontSize: 16 }}>
                  {icon}
                </Box>
              </IconButton>
            ))}
          </Box>
        </Grid>
      </Grid>
    </Box>
  )

  /**
   * ✅ Render settings section
   */
  const renderSettings = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('categories.form.settings')}
      </Typography>

      <Grid container spacing={2}>
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            type="number"
            label={t('categories.preparationTime')}
            value={formData.preparationTime}
            onChange={handleInputChange('preparationTime')}
            error={!!errors.preparationTime}
            helperText={errors.preparationTime}
            placeholder={t('categories.form.preparationTimePlaceholder')}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <TimeIcon />
                </InputAdornment>
              )
            }}
          />
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            type="number"
            label={t('categories.displayOrder')}
            value={formData.displayOrder}
            onChange={handleInputChange('displayOrder')}
            error={!!errors.displayOrder}
            helperText={errors.displayOrder}
            placeholder={t('categories.form.displayOrderPlaceholder')}
          />
        </Grid>

        <Grid size={{ xs: 12 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.showInKitchen}
                  onChange={handleSwitchChange('showInKitchen')}
                  color="primary"
                />
              }
              label={t('categories.showInKitchen')}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.showInMenu}
                  onChange={handleSwitchChange('showInMenu')}
                  color="primary"
                />
              }
              label={t('categories.showInMenu')}
            />
          </Box>
        </Grid>
      </Grid>
    </Box>
  )

  // ========== 7. MAIN RENDER ==========
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
    >
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {dialogTitle}
        </DialogTitle>

        <DialogContent dividers>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Basic Information */}
            {renderBasicInfo()}

            <Divider />

            {/* Appearance */}
            {renderAppearance()}

            <Divider />

            {/* Settings */}
            {renderSettings()}
          </Box>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClose}
            disabled={submitting}
            startIcon={<CloseIcon />}
          >
            {t('categories.form.cancel')}
          </Button>

          <Button
            type="submit"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={16} /> : <SaveIcon />}
          >
            {submitButtonText}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  )
}
