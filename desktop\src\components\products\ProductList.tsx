// Product List Component - DEVELOPMENT_RULES.md'ye uygun
// ✅ Kural 14: Component Structure Pattern
// ✅ Kural 4: i18n kullanımı
// ✅ Kural 19: Tema sistemi kullanımı

import React, { useState, useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Avatar,
  Chip,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  Tooltip
} from '@mui/material'
import {
  Edit as EditIcon,
  Delete as DeleteIcon,

  Image as ImageIcon
} from '@mui/icons-material'
import { useProductStore } from '../../store/useProductStore'
import { Product } from '@shared/types/ProductTypes'
import { CURRENCY } from '@shared/constants'

// ✅ Kural 14: Component props interface
interface ProductListProps {
  onEditProduct: (product: Product) => void
  onDeleteSuccess: (message: string) => void
  onDeleteError: (message: string) => void
}

// ✅ Kural 14: Standard component structure
export const ProductList: React.FC<ProductListProps> = ({
  onEditProduct,
  onDeleteSuccess,
  onDeleteError
}) => {
  // ========== 1. HOOKS ==========
  // ✅ i18n hook
  const { t } = useTranslation()
  
  // ✅ Theme hook
  const theme = useTheme()
  // const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // ✅ Store hooks
  const {
    products,
    loading,
    error,
    pagination,
    filters,
    fetchProducts,
    deleteProduct,
    setFilters
  } = useProductStore()
  
  // ✅ Local state hooks
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [productToDelete, setProductToDelete] = useState<Product | null>(null)
  const [deleting, setDeleting] = useState(false)
  
  // ========== 2. COMPUTED VALUES ==========
  
  /**
   * ✅ Kural 17: useMemo for expensive calculations
   */
  const isEmpty = useMemo(() => {
    return !loading && products.length === 0
  }, [loading, products.length])
  
  const isEmptySearch = useMemo(() => {
    return isEmpty && (filters.search || filters.categoryId)
  }, [isEmpty, filters.search, filters.categoryId])
  
  // ========== 3. EVENT HANDLERS ==========
  
  /**
   * ✅ Handle page change
   */
  const handlePageChange = useCallback((_: unknown, newPage: number) => {
    setFilters({ page: newPage + 1 }) // MUI uses 0-based, our API uses 1-based
    fetchProducts({ page: newPage + 1 })
  }, [setFilters, fetchProducts])
  
  /**
   * ✅ Handle rows per page change
   */
  const handleRowsPerPageChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newLimit = parseInt(event.target.value, 10)
    setFilters({ limit: newLimit, page: 1 })
    fetchProducts({ limit: newLimit, page: 1 })
  }, [setFilters, fetchProducts])
  
  /**
   * ✅ Handle delete click
   */
  const handleDeleteClick = useCallback((product: Product) => {
    setProductToDelete(product)
    setDeleteDialogOpen(true)
  }, [])
  
  /**
   * ✅ Handle delete confirm
   */
  const handleDeleteConfirm = useCallback(async () => {
    if (!productToDelete) return
    
    setDeleting(true)
    
    try {
      await deleteProduct(productToDelete.id)
      setDeleteDialogOpen(false)
      setProductToDelete(null)
      onDeleteSuccess(t('products.messages.deleteSuccess') || 'Ürün başarıyla silindi')
    } catch (error) {
      onDeleteError(t('products.messages.deleteError') || 'Ürün silinirken hata oluştu')
    } finally {
      setDeleting(false)
    }
  }, [productToDelete, deleteProduct, onDeleteSuccess, onDeleteError, t])
  
  /**
   * ✅ Handle delete cancel
   */
  const handleDeleteCancel = useCallback(() => {
    setDeleteDialogOpen(false)
    setProductToDelete(null)
  }, [])
  
  // ========== 4. RENDER HELPERS ==========
  
  /**
   * ✅ Render product image
   */
  const renderProductImage = useCallback((product: Product) => {
    if (product.image) {
      return (
        <Avatar
          src={product.image}
          alt={product.name}
          sx={{ width: 40, height: 40 }}
        >
          <ImageIcon />
        </Avatar>
      )
    }
    
    return (
      <Avatar sx={{ width: 40, height: 40, bgcolor: 'grey.300' }}>
        <ImageIcon />
      </Avatar>
    )
  }, [])
  
  /**
   * ✅ Render product status
   */
  const renderProductStatus = useCallback((product: Product) => {
    if (!product.available) {
      return (
        <Chip
          label={t('products.table.outOfStock')}
          color="error"
          size="small"
        />
      )
    }
    
    if (!product.sellable) {
      return (
        <Chip
          label={t('products.filters.notSellable')}
          color="warning"
          size="small"
        />
      )
    }
    
    return (
      <Chip
        label={t('products.table.inStock')}
        color="success"
        size="small"
      />
    )
  }, [t])
  
  /**
   * ✅ Render product price
   */
  const renderProductPrice = useCallback((product: Product) => {
    return (
      <Typography variant="body2" fontWeight="medium">
        {product.basePrice.toFixed(CURRENCY.DECIMAL_PLACES)} {CURRENCY.SYMBOL}
      </Typography>
    )
  }, [])
  
  /**
   * ✅ Render action buttons
   */
  const renderActions = useCallback((product: Product) => (
    <Box sx={{ display: 'flex', gap: 0.5 }}>
      <Tooltip title={t('products.actions.edit')}>
        <IconButton
          size="small"
          onClick={() => onEditProduct(product)}
          color="primary"
        >
          <EditIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      
      <Tooltip title={t('products.actions.delete')}>
        <IconButton
          size="small"
          onClick={() => handleDeleteClick(product)}
          color="error"
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  ), [t, onEditProduct, handleDeleteClick])
  
  /**
   * ✅ Render loading state
   */
  const renderLoading = () => (
    <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: 200,
      flexDirection: 'column',
      gap: 2
    }}>
      <CircularProgress />
      <Typography color="text.secondary">
        {t('products.list.loading')}
      </Typography>
    </Box>
  )
  
  /**
   * ✅ Render error state
   */
  const renderError = () => (
    <Alert
      severity="error"
      action={
        <Button color="inherit" size="small" onClick={() => fetchProducts()}>
          {t('products.list.retry')}
        </Button>
      }
    >
      {error || t('products.list.error')}
    </Alert>
  )
  
  /**
   * ✅ Render empty state
   */
  const renderEmpty = () => (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 200,
      gap: 2
    }}>
      <Typography variant="h6" color="text.secondary">
        {isEmptySearch ? t('products.list.emptySearch') : t('products.list.empty')}
      </Typography>
      
      {isEmptySearch && (
        <Button
          variant="outlined"
          onClick={() => {
            setFilters({ search: '', categoryId: undefined })
            fetchProducts({ search: '', categoryId: undefined })
          }}
        >
          {t('products.filters.clearFilters')}
        </Button>
      )}
    </Box>
  )
  
  /**
   * ✅ Render delete confirmation dialog
   */
  const renderDeleteDialog = () => (
    <Dialog
      open={deleteDialogOpen}
      onClose={handleDeleteCancel}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        {t('products.form.delete')}
      </DialogTitle>
      
      <DialogContent>
        <Typography>
          {t('products.messages.deleteConfirm')}
        </Typography>
        
        {productToDelete && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            <strong>{productToDelete.name}</strong> ({productToDelete.code})
          </Typography>
        )}
        
        <Typography variant="body2" color="warning.main" sx={{ mt: 2 }}>
          {t('products.messages.deleteWarning')}
        </Typography>
      </DialogContent>
      
      <DialogActions>
        <Button
          onClick={handleDeleteCancel}
          disabled={deleting}
        >
          {t('common.cancel')}
        </Button>
        
        <Button
          onClick={handleDeleteConfirm}
          color="error"
          variant="contained"
          disabled={deleting}
          startIcon={deleting ? <CircularProgress size={16} /> : <DeleteIcon />}
        >
          {deleting ? t('products.form.deleting') : t('common.delete')}
        </Button>
      </DialogActions>
    </Dialog>
  )
  
  // ========== 5. MAIN RENDER ==========
  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Error State */}
      {error && !loading && renderError()}
      
      {/* Loading State */}
      {loading && renderLoading()}
      
      {/* Empty State */}
      {isEmpty && !loading && !error && renderEmpty()}
      
      {/* Table Content */}
      {!loading && !error && !isEmpty && (
        <>
          <TableContainer sx={{ flex: 1 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>{t('products.table.columns.image')}</TableCell>
                  <TableCell>{t('products.table.columns.name')}</TableCell>
                  <TableCell>{t('products.table.columns.code')}</TableCell>
                  <TableCell>{t('products.table.columns.category')}</TableCell>
                  <TableCell>{t('products.table.columns.price')}</TableCell>
                  <TableCell>{t('products.table.columns.status')}</TableCell>
                  <TableCell align="center">{t('products.table.columns.actions')}</TableCell>
                </TableRow>
              </TableHead>
              
              <TableBody>
                {products.map((product) => (
                  <TableRow key={product.id} hover>
                    <TableCell>
                      {renderProductImage(product)}
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {product.name}
                      </Typography>
                      {product.shortDescription && (
                        <Typography variant="caption" color="text.secondary">
                          {product.shortDescription}
                        </Typography>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {product.code}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      <Typography variant="body2">
                        {product.category?.name || '-'}
                      </Typography>
                    </TableCell>
                    
                    <TableCell>
                      {renderProductPrice(product)}
                    </TableCell>
                    
                    <TableCell>
                      {renderProductStatus(product)}
                    </TableCell>
                    
                    <TableCell align="center">
                      {renderActions(product)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          {/* Pagination */}
          <TablePagination
            component="div"
            count={pagination.total}
            page={pagination.page - 1} // MUI uses 0-based, our API uses 1-based
            onPageChange={handlePageChange}
            rowsPerPage={pagination.limit}
            onRowsPerPageChange={handleRowsPerPageChange}
            rowsPerPageOptions={[10, 20, 50, 100]}
            labelRowsPerPage={t('products.pagination.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} ${t('products.pagination.of')} ${count}`
            }
          />
        </>
      )}
      
      {/* Delete Confirmation Dialog */}
      {renderDeleteDialog()}
    </Card>
  )
}
